package com.bx.implatform.Jenasi.product.controller;


import com.bx.implatform.Jenasi.product.ApiResponse;
import com.bx.implatform.Jenasi.product.dto.ProductPictureRequest;
import com.bx.implatform.Jenasi.product.vo.ProductPictureVO;
import com.bx.implatform.result.Result;
import com.bx.implatform.result.ResultUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/product")
public class ProductPictureController {

    /**
     * 获取商品图片
     * @return
     */
    @PostMapping("/getProductPicture")
    public Result<List<ProductPictureVO>> getProductPicture(@RequestBody ProductPictureRequest productPictureRequest) {
        // 打印请求参数
        //log.info("请求参数: {}", productPictureRequest);
        // 检查请求参数
        if (productPictureRequest == null ||productPictureRequest.getProductName() == null) {
            return new Result<>(400, "请求参数错误", null);
        }
        RestTemplate restTemplate = new RestTemplate();
        // 发送 POST 请求
        String response = restTemplate.postForObject(
                "http://192.168.1.173:8123/api/box/getProductPictureUrl",
                productPictureRequest,
                String.class
        );

        // 使用 ObjectMapper 将 JSON 字符串转换为 ApiResponse
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            // 反序列化为 ApiResponse 对象
            ApiResponse<List<ProductPictureVO>> apiResponse = objectMapper.readValue(
                    response,
                    new TypeReference<ApiResponse<List<ProductPictureVO>>>() {}
            );

            // 从 ApiResponse 中提取 data 字段
            List<ProductPictureVO> productPictures = apiResponse.getData();

            // 返回成功结果
            return ResultUtils.success(productPictures);
        } catch (Exception e) {
            // 处理异常情况
            return new Result<>(400, "请求失败: " + e.getMessage(), null);
        }
    }
}
