package com.bx.implatform.Jenasi.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.bx.implatform.dto.GroupMessageDTO;
import com.bx.implatform.entity.GroupMessage;
import com.bx.implatform.vo.GroupMessageVO;

import java.util.List;

public interface MyGroupMessageService extends IService<GroupMessage> {
    /**
     * 发送群聊消息(高并发接口，查询mysql接口都要进行缓存)
     *
     * @param dto 群聊消息
     * @return 群聊id
     */
    GroupMessageVO sendMessage(GroupMessageDTO dto);

    /*
    根据发送者ID查询相关消息
     */
    List<GroupMessage> getMessagesBySenderId(Long sendId);
}
