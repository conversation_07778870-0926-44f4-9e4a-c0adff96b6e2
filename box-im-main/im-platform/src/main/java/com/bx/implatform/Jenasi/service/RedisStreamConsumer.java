package com.bx.implatform.Jenasi.service;


import com.bx.implatform.Jenasi.ai.AIMsg;
import com.bx.implatform.Jenasi.util.MessageUtils;
import com.bx.implatform.Jenasi.util.PausableTaskControl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.RedisSystemException;
import org.springframework.data.redis.connection.stream.Consumer;
import org.springframework.data.redis.connection.stream.MapRecord;
import org.springframework.data.redis.connection.stream.ReadOffset;
import org.springframework.data.redis.connection.stream.RecordId;
import org.springframework.data.redis.connection.stream.StreamOffset;
import org.springframework.data.redis.connection.stream.StreamReadOptions;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Slf4j
@Component
@RequiredArgsConstructor
public class RedisStreamConsumer implements InitializingBean {

    private final StringRedisTemplate redisTemplate;

    private final PausableTaskControl pausableTaskControl;

    private final MessageUtils messageUtils;

    @Value("${redis.privateChannel}")
    private String privateChannel;

    @Value("${redis.groupChannel}")
    private String groupChannel;

    // Redis Stream的Key，消费组名称以及消费者名称
    @Value("${redis.msgQueue}")
    private String STREAM_KEY;

    @Value("${redis.handle}")
    private String handle;


    private final String GROUP = "myGroup";
    private final String CONSUMER_NAME = UUID.randomUUID().toString(); // 每个消费者使用唯一的UUID作为其名称

    // 消费超时时间，5秒内无消息则阻塞
    private final Duration BLOCK_TIMEOUT = Duration.ofSeconds(5);

    // 控制消费者运行的标志
    private volatile boolean running = true;

    @Override
    public void afterPropertiesSet() {
        // 初始化消费组，如果消费组不存在则创建
        createGroupIfNotExists();

        // 启动消费线程
        startConsuming();
    }

    /**
     * 创建消费组，如果消费组已存在则跳过创建。
     * 该方法会在Redis中检查是否已有对应的消费组，如果没有则创建。
     */
    private void createGroupIfNotExists() {
        try {
            // 检查流是否存在，如果不存在，则添加一个初始化的消息来创建流
            Boolean hasStream = redisTemplate.hasKey(STREAM_KEY);
            if (Boolean.FALSE.equals(hasStream)) {
                // 创建一个空消息，初始化Stream
                redisTemplate.opsForStream().add(STREAM_KEY, Collections.singletonMap("init", "1"));
            }

            // 检查消费组是否已经存在
            try {
                redisTemplate.opsForStream().createGroup(STREAM_KEY, ReadOffset.from("0"), GROUP);
                System.out.println("消费组创建成功");
            } catch (Exception e) {
                if (e.getMessage().contains("BUSYGROUP")) {
                    System.out.println("消费组已存在，跳过创建");
                }
            }
        } catch (RedisSystemException e) {
            // 处理异常并记录日志
            System.err.println("创建消费组失败：" + e.getMessage());
            throw e;
        }
    }

    /**
     * 启动消费线程，读取Redis Stream中的消息。
     * 每次从Stream读取最多10条消息，如果有消息则进行处理并确认消费。
     */
    private void startConsuming() {
        // 创建消费线程
        Thread consumerThread = new Thread(() -> {
            System.out.println("消费者线程启动：" + CONSUMER_NAME);
            while (running) {
                try {
                    pausableTaskControl.checkPause();  // 检查暂停状态，线程会阻塞等待直到恢复
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }


                try {
                    // 从Stream中读取消息
                    List<MapRecord<String, Object, Object>> records = redisTemplate.opsForStream().read(Consumer.from(GROUP, CONSUMER_NAME), StreamReadOptions.empty().block(BLOCK_TIMEOUT).count(1), StreamOffset.create(STREAM_KEY, ReadOffset.lastConsumed()));
                    // 如果有消息，则处理这些消息
                    if (records != null && !records.isEmpty()) {
                        for (MapRecord<String, Object, Object> record : records) {
                            // 处理每一条消息
                            System.out.println("消息队列拉取到消息：" + record.getId() + " 内容：" + record.getValue());

                            Long increment = redisTemplate.opsForValue().increment(handle);  // 计数器+1
                            if (increment >= 2L) {
                                pausableTaskControl.pause();  // 请求暂停拉取任务
                            }

                            // 处理逻辑
                            handleMessage(record.getId(), record.getValue());

                            // 手动确认消息已处理
                            redisTemplate.opsForStream().acknowledge(STREAM_KEY, GROUP, record.getId());
                        }
                    }
                } catch (Exception e) {
                    // 出现异常时输出日志，便于排查
                    e.printStackTrace();
                }
            }
        });

        // 设置为守护线程，主程序退出时自动关闭
        consumerThread.setDaemon(true);
        consumerThread.start();
    }

    /**
     * 处理从Stream中读取到的消息
     *
     * @param value Redis Stream中的消息内容
     */
    private void handleMessage(RecordId id, Map<Object, Object> value) {
        // 异步处理消息，使用线程池

        //处理消息，异步消息
        extractMessageAndProcess(value, id);

    }

    /**
     * 删除消息后，计数器减一，并恢复工作线程
     *
     * @param id
     */
    private void cleanupAfterProcessing(RecordId id) {
        redisTemplate.opsForValue().decrement(handle);  // 计数器减一
        redisTemplate.opsForStream().delete(STREAM_KEY, id); // 删除消息
        pausableTaskControl.resume();// 恢复工作线程
    }

    //业务处理1
    private void extractMessageAndProcess(Map<Object, Object> value, RecordId msgId) {
//        业务处理：{nickName=易志文, source=2, message=3, type=AiPrivateDev, uuid=b1bf14d2-0020-4ae8-b75e-2f1a5e07f461}
        String nickName = (String) value.get("nickName");
        String source = (String) value.get("source");
        String message = (String) value.get("message");
        String type = (String) value.get("type");
        String uuid = (String) value.get("uuid");

        this.handleBusinessAsync(nickName, message, source, type, msgId);

    }


    //处理业务2
    private void handleBusinessAsync(String nickName, String msg, String source, String channel, RecordId msgId) {
        log.info("开始处理{}的问题……", nickName);

        notifyStartProcessingAsync(nickName, source, channel); // 异步处理，提示处理

        AIMsg.getAIMsgAsync(nickName, msg, answer -> {
            cleanupAfterProcessing(msgId);  // 删除消息后，计数器减一，并恢复工作线程
            sendAnswerAsync(source, channel, answer);   // 异步处理，回复
        }, error -> {
            cleanupAfterProcessing(msgId);  // 删除消息后，计数器减一，并恢复工作线程
            sendAnswerAsync(source, channel, error);
        });

    }


    /**
     * 异步处理，回复
     *
     * @param nickName
     * @param source
     * @param channel
     */
    private void notifyStartProcessingAsync(String nickName, String source, String channel) {
        messageUtils.sendMessage("开始处理 " + nickName + " 的提问…", source, channel);
    }


    /**
     * 异步处理，回复
     *
     * @param source
     * @param channel
     * @param answer
     */
    private void sendAnswerAsync(String source, String channel, String answer) {
        messageUtils.sendMessage(answer, source, channel);
    }


    /**
     * 停止消费者线程
     */
    public void stop() {
        running = false;
    }

}
