package com.bx.implatform.Jenasi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bx.imclient.IMClient;
import com.bx.implatform.Jenasi.dto.PageResult;
import com.bx.implatform.Jenasi.service.MyUserServic;
import com.bx.implatform.entity.User;
import com.bx.implatform.mapper.UserMapper;
import com.bx.implatform.util.BeanUtils;
import com.bx.implatform.vo.UserVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;
@Slf4j
@Service
@RequiredArgsConstructor
public class MyUserServiceImpl extends ServiceImpl<UserMapper, User> implements MyUserServic {
    private final IMClient imClient;

    private final UserMapper userMapper;

    /**
     * 根据用户 ID 查询用户名
     *
     * @param userId 用户 ID
     * @return 用户名
     */
    @Override
    public String getUsernameById(Long userId) {
        // 调用 UserMapper 的自定义方法 selectUsernameById 查询用户名
        return userMapper.selectUsernameById(userId);
    }

    /**
     * 分页查询所有用户，并设置用户的在线状态
     *
     * @param current 当前页码
     * @param size    每页大小
     * @return 包含分页信息和用户数据的结果对象
     */
    @Override
    public PageResult<UserVO> findAllUsers(Integer current, Integer size) {
        // 创建分页对象
        Page<User> page = new Page<>(current, size);
        // 执行分页查询
        Page<User> userPage = this.baseMapper.selectPage(page, new QueryWrapper<>());

        // 转换为UserVO列表并设置在线状态
        List<UserVO> userVOS = userPage.getRecords().stream().map(user -> {
            UserVO vo = new UserVO();
            BeanUtils.copyProperties(user, vo);
            vo.setOnline(imClient.isOnline(user.getId()));
            return vo;
        }).collect(Collectors.toList());
        // 封装分页结果,返回 PageResult 对象
        return new PageResult<>(
                userPage.getTotal(), //总记录数
                userPage.getPages(), //总页数
                current, //当前页码
                size, //每页大小
                userVOS  //转换后的用户数据列表
        );
    }
}
