package com.bx.implatform.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

@Data
@Schema(description = "手机号登录参数")
public class PhoneLoginDTO {
    
    @Schema(description = "手机号")
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    @Schema(description = "密码")
    private String password;

    @Schema(description = "验证码")
    private String code;

    @Schema(description = "登录类型：password-密码登录，code-验证码登录")
    @NotBlank(message = "登录类型不能为空")
    private String loginType;

    @Schema(description = "终端类型：0-PC，1-Android，2-IOS")
    private Integer terminal;
} 