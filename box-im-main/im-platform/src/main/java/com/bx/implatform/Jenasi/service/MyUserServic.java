package com.bx.implatform.Jenasi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bx.implatform.Jenasi.dto.PageResult;
import com.bx.implatform.entity.User;
import com.bx.implatform.vo.UserVO;

public interface MyUserServic extends IService<User> {
    /*
   新增，实现直接加好友功能
    */
    String getUsernameById(Long userId);

    PageResult<UserVO> findAllUsers(Integer current, Integer size);
}
