package com.bx.implatform.Jenasi.util;

import com.bx.implatform.dto.GroupMessageDTO;
import com.bx.implatform.dto.PrivateMessageDTO;
import com.bx.implatform.service.GroupMessageService;
import com.bx.implatform.service.PrivateMessageService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Component
public class MessageUtils {

    private static final ObjectMapper mapper = new ObjectMapper();

    @Autowired
    @Lazy
    private PrivateMessageService privateMessageService;

    @Autowired
    @Lazy
    private GroupMessageService groupMessageService;

    @Value("${redis.privateChannel}")
    private String privateChannel;

    @Value("${redis.groupChannel}")
    private String groupChannel;

    // Redis Stream的Key，消费组名称以及消费者名称
    @Value("${redis.msgQueue}")
    private String STREAM_KEY;


    /**
     * 发送消息的方法，根据不同的频道类型，将消息发送给私聊或群聊对象
     *
     * @param msg     要发送的消息内容
     * @param source  消息的来源标识
     * @param channel 消息的频道类型，支持 "AiPrivate" 和 "AiGroup"
     */
    public void sendMessage(String msg, String source, String channel) {
        if (privateChannel.equals(channel)) {
            PrivateMessageDTO privateMessageDTO = new PrivateMessageDTO();
            privateMessageDTO.setContent(msg); // 回复的消息
            privateMessageDTO.setType(0);   // 消息类型
            privateMessageDTO.setRecvId(Long.valueOf(source));  // 实际的接收者ID
            privateMessageService.sendMessage(privateMessageDTO);

        } else if (groupChannel.equals(channel)) {
            GroupMessageDTO groupMessageDTO = new GroupMessageDTO();
            groupMessageDTO.setContent(msg);
            groupMessageDTO.setGroupId(Long.valueOf(source));
            groupMessageDTO.setType(0);

            List<Long> objects = new ArrayList<>();
            groupMessageDTO.setAtUserIds(objects);

            groupMessageService.sendMessage(groupMessageDTO);
        }

    }

    //设置已读
    public void readeMessage(String source, String channel) {
        if (privateChannel.equals(channel)) {
            privateMessageService.readedMessage(Long.valueOf(source));
        } else if (groupChannel.equals(channel)) {
            groupMessageService.readedMessage(Long.valueOf(source));
        }
    }


}
