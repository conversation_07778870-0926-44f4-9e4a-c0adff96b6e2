package com.bx.implatform.Jenasi.ai;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

public class AIMsg {

    private static final Logger log = LoggerFactory.getLogger(AIMsg.class);

    private static final String AI_KEY = "app-HJnrkEWlawBrLbIsVtVI5dJe";
    private static final String AI_URL = "http://192.168.1.177:1919/gateway";
    private static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");

    private static final ObjectMapper MAPPER = new ObjectMapper();

    private static final OkHttpClient CLIENT = new OkHttpClient.Builder()
            .connectTimeout(300, TimeUnit.SECONDS)
            .readTimeout(300, TimeUnit.SECONDS)
            .writeTimeout(300, TimeUnit.SECONDS)
            .build();

    /**
     * 异步获取 AI 回复，处理成功/失败通过回调处理
     *
     * @param nickName 用户昵称
     * @param userMessage 用户消息
     * @param onSuccess 成功时回调（参数为 AI 返回内容）
     * @param onFailure 失败时回调（参数为异常信息）
     */
    public static void getAIMsgAsync(String nickName, String userMessage,
                                     Consumer<String> onSuccess,
                                     Consumer<String> onFailure) {

        log.info("异步发送 AI 请求：用户={}，问题={}", nickName, userMessage);
        Request request = buildRequest(nickName, userMessage);

        CLIENT.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                log.error("AI 请求失败", e);
                if (onFailure != null) {
                    onFailure.accept("调用 AI 接口异常: " + e.getMessage());
                }
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try (response) {
                    if (!response.isSuccessful()) {
                        String errorBody = response.body() != null ? response.body().string() : "无响应体";
                        log.warn("AI 请求失败，状态码：{}，响应体：{}", response.code(), errorBody);
                        if (onFailure != null) {
                            onFailure.accept("请求失败，状态码：" + response.code());
                        }
                        return;
                    }

                    String responseBody = response.body().string();
                    log.debug("AI 响应体: {}", responseBody);

                    JsonNode jsonNode = MAPPER.readTree(responseBody);
                    String answer = jsonNode.has("answer")
                            ? jsonNode.get("answer").asText()
                            : "抱歉，我现在无法回答";

                    log.info("AI 回复：{}", answer);
                    if (onSuccess != null) {
                        onSuccess.accept(answer);  // 调用回调处理成功
                    }

                } catch (Exception ex) {
                    log.error("AI 响应解析异常", ex);
                    if (onFailure != null) {
                        onFailure.accept("AI 响应解析异常: " + ex.getMessage());   // 调用回调处理失败
                    }
                }
            }
        });
    }

    private static Request buildRequest(String nickName, String userMessage) {
        ObjectNode bodyNode = MAPPER.createObjectNode();
        bodyNode.putObject("inputs");
        bodyNode.put("query", userMessage);
        bodyNode.put("response_mode", "blocking");
        bodyNode.put("conversation_id", "");
        bodyNode.put("user", nickName);

        RequestBody requestBody = RequestBody.create(bodyNode.toString(), JSON);

        return new Request.Builder()
                .url(AI_URL)
                .addHeader("Authorization", "Bearer " + AI_KEY)
                .addHeader("Accept", "application/json")
                .post(requestBody)
                .build();
    }



    // 测试
    public static void main(String[] args) {
        getAIMsgAsync("小明", "你好",
                answer -> System.out.println("AI 回复：" + answer),
                error -> System.out.println("AI 请求失败：" + error));
    }
}
