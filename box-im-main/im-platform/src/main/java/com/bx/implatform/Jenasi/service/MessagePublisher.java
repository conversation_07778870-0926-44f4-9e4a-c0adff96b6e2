package com.bx.implatform.Jenasi.service;

import cn.hutool.core.lang.UUID;
import com.bx.implatform.Jenasi.util.MessageUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.bridge.MessageUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.connection.stream.RecordId;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;
import java.util.HashMap;

@Service
@RequiredArgsConstructor
@Slf4j
public class MessagePublisher {

    private final StringRedisTemplate redisTemplate;
    private final ObjectMapper mapper = new ObjectMapper();

    @Value("${redis.msgQueue}")
    String msgQueue;

    @Resource
    MessageUtils messageUtils;


    public void publishMessage(String type, Long userId, String nickName, String message) {

        HashMap<String, String> map = new HashMap<>();
        map.put("source", userId.toString());   // source 与 群Id 都是这个，用此回复  发送者 或者发送的群
        map.put("nickName", nickName);
        map.put("message", message);
        map.put("type", type);  // 消息类型，区分是群聊还是私聊
        map.put("time", ZonedDateTime.now() + "");
        map.put("uuid", UUID.randomUUID().toString());

        Long count = redisTemplate.opsForStream().size(msgQueue);
        if (count >= 2) {
            messageUtils.sendMessage("我还在思考中，前面还有" + count + "条消息等着我回复呢，稍等一下~",userId.toString(),type);
        }

        //将消息放入队列
        RecordId add = redisTemplate.opsForStream().add(msgQueue, map);

        messageUtils.readeMessage(userId.toString(),type);


        log.info("推送消息队列内容: {},ID:{}", map, add);
        // 检查是否成功添加到流中
        if (add == null) {
            throw new RuntimeException("消息添加到流失败");
        }

        log.info("------推送消息: {}", message);


    }
}
