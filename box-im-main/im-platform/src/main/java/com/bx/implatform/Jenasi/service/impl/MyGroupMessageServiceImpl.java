package com.bx.implatform.Jenasi.service.impl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bx.imclient.IMClient;
import com.bx.imcommon.model.IMGroupMessage;
import com.bx.imcommon.model.IMUserInfo;
import com.bx.imcommon.util.CommaTextUtils;
import com.bx.implatform.Jenasi.service.MyGroupMessageService;
import com.bx.implatform.dto.GroupMessageDTO;
import com.bx.implatform.entity.Group;
import com.bx.implatform.entity.GroupMember;
import com.bx.implatform.entity.GroupMessage;
import com.bx.implatform.enums.MessageStatus;
import com.bx.implatform.enums.MessageType;
import com.bx.implatform.exception.GlobalException;
import com.bx.implatform.mapper.GroupMessageMapper;
import com.bx.implatform.service.GroupMemberService;
import com.bx.implatform.service.GroupService;
import com.bx.implatform.session.SessionContext;
import com.bx.implatform.session.UserSession;
import com.bx.implatform.util.BeanUtils;
import com.bx.implatform.util.SensitiveFilterUtil;
import com.bx.implatform.vo.GroupMessageVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class MyGroupMessageServiceImpl extends ServiceImpl<GroupMessageMapper, GroupMessage> implements MyGroupMessageService {

    private final GroupService groupService;
    private final GroupMemberService groupMemberService; //用于检查成员状态
    private final RedisTemplate<String, Object> redisTemplate; // Redis于缓存操作
    private final IMClient imClient; // 即时通讯客户端，用于发送消息
    private final SensitiveFilterUtil sensitiveFilterUtil; // 敏感词过滤工具

    /**
     * 发送群聊消息
     *
     * @param dto 包含群聊消息数据的 DTO 对象
     * @return 发送的消息 VO 对象
     */
    @Override
    public GroupMessageVO sendMessage(GroupMessageDTO dto) {
        // 获取当前用户的会话信息
        UserSession session = SessionContext.getSession();

        // 如果会话为空，创建一个默认会话（模拟用户）
        if (session == null) {
            session = new UserSession();
            session.setUserId(1L); // 默认用户 ID 为 1
            session.setTerminal(1); // 默认终端类型
        }

        // 根据群组 ID 获取并校验群组是否存在
        Group group = groupService.getAndCheckById(dto.getGroupId());

        // 检查当前用户是否在群聊中
        GroupMember member = groupMemberService.findByGroupAndUserId(dto.getGroupId(), session.getUserId());
        if (!session.getUserId().equals(1L)) { // 非 AI 用户需要权限检查
            if (Objects.isNull(member) || member.getQuit()) {
                throw new GlobalException("您已不在群聊里面，无法发送消息");
            }
        }

        // 获取群聊中的所有成员 ID 列表
        List<Long> userIds = groupMemberService.findUserIdsByGroupId(group.getId());

        // 过滤掉发送者自己
        UserSession finalSession = session;
        userIds = userIds.stream()
                .filter(id -> !finalSession.getUserId().equals(id))
                .collect(Collectors.toList());

        // 构造消息对象并保存到数据库
        GroupMessage msg = BeanUtils.copyProperties(dto, GroupMessage.class);
        msg.setSendId(session.getUserId()); // 设置发送者 ID
        msg.setSendTime(new Date()); // 设置发送时间
        msg.setSendNickName(member.getShowNickName()); // 设置发送者的昵称
        msg.setAtUserIds(CommaTextUtils.asText(dto.getAtUserIds())); // 设置 @ 的用户 ID 列表

        // 如果消息内容包含 @AI，则标记为未读以便自动回复
        if (dto.getContent().contains("@Jenasi智能体")) {
            msg.setStatus(MessageStatus.UNREAD.code());
        } else {
            msg.setStatus(MessageStatus.SENDED.code());
        }

        // 如果是文本消息，过滤敏感词
        if (MessageType.TEXT.code().equals(dto.getType())) {
            msg.setContent(sensitiveFilterUtil.filter(dto.getContent()));
        }

        // 保存消息到数据库
        this.save(msg);

        // 构造返回的 VO 对象
        GroupMessageVO msgInfo = BeanUtils.copyProperties(msg, GroupMessageVO.class);
        msgInfo.setAtUserIds(dto.getAtUserIds());

        // 构造即时通讯消息对象
        IMGroupMessage<GroupMessageVO> sendMessage = new IMGroupMessage<>();
        sendMessage.setSender(new IMUserInfo(session.getUserId(), session.getTerminal())); // 设置发送者信息
        sendMessage.setRecvIds(userIds); // 设置接收者 ID 列表
        sendMessage.setSendResult(false); // 设置发送结果为 false
        sendMessage.setData(msgInfo); // 设置消息数据
        // 调用即时通讯客户端发送消息
        imClient.sendGroupMessage(sendMessage);

        // 记录日志
        log.info("发送群聊消息，发送id:{},群聊id:{},内容:{}", session.getUserId(), dto.getGroupId(), dto.getContent());

        return msgInfo; // 返回消息 VO 对象
    }

    /**
     * 根据发送者 ID 查询消息记录
     *
     * @param sendId 发送者 ID
     * @return 消息列表
     */
    @Override
    public List<GroupMessage> getMessagesBySenderId(Long sendId) {
        // 创建查询条件
        QueryWrapper<GroupMessage> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("send_id", sendId) // 查询条件：send_id = ?
                .select("send_nick_name", "content", "recv_ids"); // 只查询指定字段

        // 执行查询并返回结果
        return baseMapper.selectList(queryWrapper);
    }
}
