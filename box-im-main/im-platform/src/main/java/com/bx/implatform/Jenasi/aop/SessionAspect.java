package com.bx.implatform.Jenasi.aop;

import com.bx.implatform.session.SessionContext;

import com.bx.implatform.session.UserSession;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

@Aspect
@Component
@Slf4j
public class SessionAspect {

    @Around("execution(public static com.bx.implatform.session.UserSession com.bx.implatform.session.SessionContext.getSession())")
    public UserSession aroundGetSession(ProceedingJoinPoint joinPoint) throws Throwable {
        System.out.println("-----------------------------------------------------------------------");
        // 获取当前线程存储的 UserSession，如果请求上下文没有的话
        UserSession session = SessionContext.getSession();
        if (session == null) {
            log.info("请求上下文没有 UserSession，尝试从 ThreadLocal 获取");
            // 如果当前没有 UserSession，我就自己创建一个
            session = new UserSession();
            session.setUserId(1L);
            session.setTerminal(1);
            session.setUserName("AI");
            session.setNickName("Ai");
        }
        // 返回修改后的 UserSession
        return session;
    }
}

