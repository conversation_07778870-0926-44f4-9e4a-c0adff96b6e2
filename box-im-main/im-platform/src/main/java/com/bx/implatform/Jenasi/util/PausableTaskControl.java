package com.bx.implatform.Jenasi.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 可控制线程暂停和恢复的工具类。
 * 线程通过调用 {@link #checkPause()} 主动检查暂停状态，
 * 当处于暂停时会阻塞等待，直到调用 {@link #resume()} 恢复继续执行。
 *
 * 设计重点：
 * - 使用 ReentrantLock + Condition 保证线程安全和等待通知机制。
 * - 通过 volatile 标记暂停状态，保证状态可见性。
 * - 线程被中断时，保证中断信号不丢失。
 * - 支持暂停和恢复的回调扩展。
 */
@Component
public class PausableTaskControl {

    private static final Logger logger = LoggerFactory.getLogger(PausableTaskControl.class);

    private final Lock lock = new ReentrantLock();
    private final Condition condition = lock.newCondition();

    /** 标志任务是否暂停，使用 volatile 保证可见性 */
    private volatile boolean paused = false;

    /** 暂停时的回调，非必需 */
    private Runnable onPauseCallback;

    /** 恢复时的回调，非必需 */
    private Runnable onResumeCallback;

    /**
     * 线程调用该方法主动检查当前是否应暂停。
     * 如果处于暂停状态，线程会阻塞等待直到恢复。
     *
     * @throws InterruptedException 如果线程等待过程中被中断，异常会向上传递，且会恢复中断状态
     */
    public void checkPause() throws InterruptedException {
        if (!paused) {
            return; // 无需暂停，直接返回
        }

        lock.lock();
        try {
            while (paused) {
                logger.debug("{} 进入暂停等待状态...", Thread.currentThread().getName());
                condition.await();
                logger.debug("{} 被唤醒，准备恢复执行...", Thread.currentThread().getName());
            }
        } catch (InterruptedException e) {
            // 恢复线程中断状态，避免中断信号丢失
            Thread.currentThread().interrupt();
            logger.warn("{} 在暂停等待时被中断", Thread.currentThread().getName());
            throw e; // 继续向上抛出中断异常
        } finally {
            lock.unlock();
        }
    }

    /**
     * 请求暂停任务。
     * 如果任务已是暂停状态，返回 false。
     *
     * @return true 如果成功从运行切换到暂停，false 如果已经是暂停状态
     */
    public boolean pause() {
        lock.lock();
        try {
            if (paused) {
                return false; // 已经暂停，无需重复操作
            }
            paused = true;
            logger.debug("请求暂停工作线程...");
            if (onPauseCallback != null) {
                try {
                    onPauseCallback.run();
                } catch (Exception e) {
                    logger.error("暂停回调执行异常", e);
                }
            }
            return true;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 请求恢复任务。
     * 如果任务已是运行状态，返回 false。
     *
     * @return true 如果成功从暂停切换到运行，false 如果已经是运行状态
     */
    public boolean resume() {
        lock.lock();
        try {
            if (!paused) {
                return false; // 已经恢复，无需操作
            }
            paused = false;
            logger.debug("请求恢复工作线程...");
            condition.signalAll(); // 唤醒所有等待线程

            if (onResumeCallback != null) {
                try {
                    onResumeCallback.run();
                } catch (Exception e) {
                    logger.error("恢复回调执行异常", e);
                }
            }
            return true;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 获取当前暂停状态。
     *
     * @return true 如果处于暂停，false 表示正常运行
     */
    public boolean isPaused() {
        return paused;
    }

    /**
     * 设置暂停时的回调函数，可选。
     *
     * @param callback 回调函数
     */
    public void setOnPauseCallback(Runnable callback) {
        this.onPauseCallback = callback;
    }

    /**
     * 设置恢复时的回调函数，可选。
     *
     * @param callback 回调函数
     */
    public void setOnResumeCallback(Runnable callback) {
        this.onResumeCallback = callback;
    }
}
