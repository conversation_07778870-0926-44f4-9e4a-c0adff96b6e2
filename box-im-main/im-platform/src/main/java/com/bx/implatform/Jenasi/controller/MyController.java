package com.bx.implatform.Jenasi.controller;

import com.bx.implatform.Jenasi.dto.MyPrivateMessageDTO;
import com.bx.implatform.Jenasi.dto.PageResult;
import com.bx.implatform.Jenasi.service.MyGroupMessageService;
import com.bx.implatform.Jenasi.service.MyPrivateMessageService;
import com.bx.implatform.Jenasi.service.impl.MyUserService;
import com.bx.implatform.Jenasi.service.MyUserServic;
import com.bx.implatform.dto.GroupMessageDTO;
import com.bx.implatform.entity.GroupMessage;
import com.bx.implatform.result.Result;
import com.bx.implatform.result.ResultUtils;
import com.bx.implatform.vo.GroupMessageVO;
import com.bx.implatform.vo.GroupVO;
import com.bx.implatform.vo.PrivateMessageVO;
import com.bx.implatform.vo.UserVO;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
public class MyController {

    private final MyUserService userService;

    private final MyUserServic MyuserServic;

    private final MyGroupMessageService mygroupMessageService;

    private final MyPrivateMessageService myprivateMessageService;

    private final MyGroupMessageService myGroupMessageService;

    @GetMapping("/userid")
    public ResponseEntity<Map<String, Long>> getUserIdByUserName(
            @RequestParam @NotBlank(message = "用户名不能为空")
            @Size(max = 20, message = "用户名不能超过20个字符")
            String userName) {
        Long userId = userService.getUserIdByUserName(userName);
        return ResponseEntity.ok(Collections.singletonMap("id", userId));
    }


    @GetMapping("/groupid")
    public List<GroupVO> searchGroups(@RequestParam(required = false) String name) {
        return userService.searchGroupsByName(name);
    }

    @GetMapping("/by-sender")
    public ResponseEntity<List<GroupMessage>> getMessagesBySender(@RequestParam Long sendId) {
        List<GroupMessage> messages = mygroupMessageService.getMessagesBySenderId(sendId);
        return ResponseEntity.ok(messages);
    }

    @PostMapping("/privatesend")
    @Operation(summary = "发送消息", description = "发送私聊消息")
    public Result<PrivateMessageVO> sendMessage(@Valid @RequestBody MyPrivateMessageDTO dto) {
        // 直接从DTO中获取userId和terminal
        Long userId = dto.getUserId();
        Integer terminal = dto.getTerminal();
        // 调用服务层的sendMessage方法
        PrivateMessageVO messageVO = myprivateMessageService.sendMessage(dto, userId, terminal);
        return ResultUtils.success(messageVO);
    }

    //群聊私发消息
    @PostMapping("/groupsend")
    @Operation(summary = "发送群聊消息", description = "发送群聊消息")
    public Result<GroupMessageVO> sendMessage(@Valid @RequestBody GroupMessageDTO vo) {
        return ResultUtils.success(myGroupMessageService.sendMessage(vo),vo.toString());
    }

    //新增接口，实现直接添加好友
    @GetMapping("/all")
    public Result<PageResult<UserVO>> getAllUsers(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "6") Integer size) {
        PageResult<UserVO> result = MyuserServic.findAllUsers(current, size);
        return ResultUtils.success(result);
    }
}


