package com.bx.implatform.Jenasi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.bx.implatform.entity.Group;
import com.bx.implatform.entity.User;
import com.bx.implatform.mapper.GroupMapper;
import com.bx.implatform.mapper.GroupMessageMapper;
import com.bx.implatform.mapper.UserMapper;
import com.bx.implatform.vo.GroupVO;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class MyUserService{
    @Autowired
    private UserMapper userMapper;

    @Autowired
    private  GroupMapper groupMapper;

    /**
     * 根据用户名查询用户 ID
     *
     * @param userName 用户名
     * @return 用户 ID
     * @throws RuntimeException 如果用户不存在，则抛出异常
     */
    public Long getUserIdByUserName(String userName) {
        // 创建一个 LambdaQueryWrapper 对象，用于封装查询条件
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        // 设置查询条件：user_name = ?
        queryWrapper.eq(User::getNickName, userName);

        // 调用 MyBatis-Plus 的 selectOne 方法，根据条件查询一条记录
        User user = userMapper.selectOne(queryWrapper);

        // 如果查询结果为空，抛出异常
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 返回用户的 ID
        return user.getId();
    }


    /**
     * 根据群名模糊查询群组信息
     *
     * @param name 群名（支持模糊查询）
     * @return 符合条件的群组列表（转换为 GroupVO 对象）
     */
    public List<GroupVO> searchGroupsByName(String name) {
        // 创建一个 QueryWrapper 对象，用于封装查询条件
        QueryWrapper<Group> queryWrapper = new QueryWrapper<>();

        // 如果 name 不为空且非空白字符，添加模糊查询条件：name LIKE '%?%'
        if (StringUtils.isNotBlank(name)) {
            queryWrapper.like("name", name);
        }

        // 调用 MyBatis-Plus 的 selectList 方法，根据条件查询多条记录
        List<Group> groups = groupMapper.selectList(queryWrapper);

        // 使用 Java 8 的流式 API 将 Group 对象转换为 GroupVO 对象
        return groups.stream().map(group -> {
            GroupVO vo = new GroupVO(); // 创建 GroupVO 对象
            vo.setId(group.getId()); // 设置群组 ID
            vo.setName(group.getName()); // 设置群组名称
            return vo; // 返回 GroupVO 对象
        }).collect(Collectors.toList()); // 收集为 List<GroupVO>
    }
}

