package com.bx.implatform.Jenasi.controller;

import com.bx.implatform.entity.User;
import com.bx.implatform.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/util")
public class UserUtilController {


    private final UserService userService;

    /**
     * 获取用户昵称byID
     */
    @GetMapping("/username/{userId}")
    public String getUsernameById(@PathVariable Long userId) {
        User user = userService.getById(userId);
        if (user == null) {
            return "";
        }
        return user.getNickName();
    }

    /**
     * 获取用户的的员工id
     */
    @GetMapping("/employeeId/{userId}")
    public String getEmployeeIdById(@PathVariable Long userId) {
        User user = userService.getById(userId);
        if (user == null) {
            return "";
        }
        long employeesId = user.getEmployeesId();
        return ""+employeesId;
    }
}
