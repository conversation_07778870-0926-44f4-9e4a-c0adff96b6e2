package com.bx.implatform.session;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/*
 * @Description
 * <AUTHOR>
 * @Date 2022/10/21
 */
public class SessionContext {

    public static UserSession getSession() {
        // 从请求上下文里获取Request对象
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        UserSession session = null;
        try {
            HttpServletRequest request = requestAttributes.getRequest();
            session = (UserSession) request.getAttribute("session");
        }catch (Exception e) {
            // 如果当前没有 UserSession，我就自己创建一个
            session = new UserSession();
            session.setUserId(1L);
            session.setTerminal(1);
            session.setUserName("Jenasi智能体");
            session.setNickName("Jenasi智能体");
        }
        return session;
    }

}
