package com.bx.implatform.Jenasi.service;


import com.baomidou.mybatisplus.extension.service.IService;

import com.bx.implatform.Jenasi.dto.MyPrivateMessageDTO;
import com.bx.implatform.entity.PrivateMessage;
import com.bx.implatform.vo.PrivateMessageVO;

public interface MyPrivateMessageService extends IService<PrivateMessage> {
    PrivateMessageVO sendMessage(MyPrivateMessageDTO dto, Long userId, Integer terminal);
}