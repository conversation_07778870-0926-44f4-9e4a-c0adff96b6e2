spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ************************************************************************************************************************
    username: root
    password: j<PERSON><PERSON>&=1688
  data:
    redis:
      host: *************
      port: 6379
      password: jenasi&=1688
      database: 1

redis:
  privateChannel: AiPrivateDev
  groupChannel: AiGroupDev
  msgQueue: processed:ai_msg_dev
  handle:  processed:ai_message_handle_dev


minio:
  endpoint: http://*************:900 #内网地址
  domain: http://jenasi.ai:900  #外网访问地址
  accessKey: QDWe46Cm2K4UmKewzZck
  secretKey: W4Mrd4GTajP28F8WS06PmGAvUUfKSTPavskCEIdq
  bucketName: box-im
  imagePath: image
  filePath: file
  videoPath: video
  expireIn: 180 # 文件过期时间,单位:天


webrtc:
  max-channel: 9 # 多人通话最大通道数量，最大不能超过16,建议值:4,9,16
  iceServers:   #coturn配置
    - urls: stun:www.boxim.online:3478
      username: admin
      credential: UrHHKNvE7nFvBTMV
    - urls: turn:www.boxim.online:3478
      username: admin
      credential: UrHHKNvE7nFvBTMV


#sms:
#  platform: aliyun  # 目前仅支持阿里云短信
#  access-key: LTAI5tEjGjBgjz5nTP4MkSvR
#  secret-key: ******************************
#  sign-name: xxx科技 # 短信签名,需在阿里云控制台审核通过
#  template-id: SMS_481125065 # 短信模版id,验证码变量必须为: ${code}


sms:
  platform: aliyun  # 目前仅支持阿里云短信
  access-key: LTAI5tBM3TG3b6bJqAyT4kKi
  secret-key: ******************************
  sign-name: 湖南简思科技有限公司 # 短信签名,需在阿里云控制台审核通过
  template-id: SMS_486615268 # 短信模版id,验证码变量必须为: ${code}

mail:
  host: smtp.163.com
  port: 465
  ssl: true
  name: 湖南简思科技有限公司
  from: <EMAIL>  # 发送方邮箱
  pass: VWQ8QhgemGAWanQQ # 授权码，邮箱后台开启授权后获得
  subject: '您收到一条验证码'
  content: '您的验证码为:  ${code} ,请勿泄露给他人，有效期为5分钟'

registration:
  mode:
    #- username #用户名注册
    - phone # 手机注册,需开启短信功能
    - email # 邮箱注册,需开启短信功能