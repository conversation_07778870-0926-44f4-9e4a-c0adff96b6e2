/.idea/
/box-im.iml
/im-server/im-server.iml
/im-platform/im-platform.iml
/im-client/im-client.iml
/im-common/im-common.iml
/im-uniapp/node_modules/
/im-web/package-lock.json
/im-uniapp/package-lock.json
# 默认忽略的文件
/shelf/
/workspace.xml

# 基于编辑器的 HTTP 客户端请求
/httpRequests/
# Datasource local storage ignored files
/dataSources/
/dataSources.local.xml
# IDE相关文件
./.idea/
.vscode/
*.swp
*.swo
*~

# Java相关
*.class
*.jar
*.war
*.ear
*.nar
hs_err_pid*
replay_pid*

# Maven相关
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# Gradle相关
.gradle/
build/
!gradle/wrapper/gradle-wrapper.jar
!**/src/main/**/build/
!**/src/test/**/build/

# Eclipse相关
.metadata
bin/
tmp/
*.tmp
*.bak
*~.nib
local.properties
.settings/
.loadpath
.recommenders
.project
.classpath
.externalToolBuilders/
*.launch
.pydevproject
.cproject
.autotools
.factorypath
.buildpath
.target
.tern-project
.texlipse
.springBeans
.recommenders/
.apt_generated/
.apt_generated_test/

# NetBeans相关
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# Node.js相关
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Vue.js相关
.cache/
.temp/
.vuepress/dist
.nuxt
.next

# UniApp相关
unpackage/
*.hap
*.apk
*.ipa

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
lib-cov
coverage
*.lcov
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
logs
*.log

# 数据库文件
*.sqlite
*.db

# 操作系统相关
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 压缩文件
*.7z
*.dmg
*.gz
*.iso
*.rar
*.tar
*.zip

# 临时文件
*.cache
*.temp

# 编译输出
out/

# IntelliJ IDEA相关
*.iml
*.ipr
*.iws
.idea_modules/
atlassian-ide-plugin.xml
com_crashlytics_export_strings.xml
crashlytics.properties
crashlytics-build.properties
fabric.properties

# 本地配置文件
application-local.yml
application-local.yaml
application-local.properties