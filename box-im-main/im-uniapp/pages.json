{"easycom": {"autoscan": true, "custom": {"^u--(.*)": "@/uni_modules/uview-plus/components/u-$1/u-$1.vue", "^up-(.*)": "@/uni_modules/uview-plus/components/u-$1/u-$1.vue", "^u-([^-].*)": "@/uni_modules/uview-plus/components/u-$1/u-$1.vue"}}, "pages": [{"path": "pages/chat/chat"}, {"path": "pages/login/login"}, {"path": "pages/register/register"}, {"path": "pages/friend/friend"}, {"path": "pages/group/group"}, {"path": "pages/mine/mine"}, {"path": "pages/workspace/workspace", "style": {"navigationBarTitleText": "工作台", "disableScroll": true, "app-plus": {"scrollIndicator": "none", "bounce": "none"}}}, {"path": "pages/common/user-info"}, {"path": "pages/chat/chat-box", "style": {"navigationStyle": "custom", "app-plus": {"softinputMode": "adjustResize"}}}, {"path": "pages/chat/chat-system"}, {"path": "pages/chat/chat-system-content"}, {"path": "pages/chat/chat-private-video"}, {"path": "pages/chat/chat-group-video"}, {"path": "pages/friend/friend-add"}, {"path": "pages/group/group-info"}, {"path": "pages/group/group-edit"}, {"path": "pages/group/group-invite"}, {"path": "pages/group/group-member"}, {"path": "pages/mine/mine-edit"}, {"path": "pages/mine/mine-password"}, {"path": "pages/mine/mine-phone"}, {"path": "pages/scan/scan"}, {"path": "pages/common/external-link"}, {"path": "pages/common/complaint"}, {"path": "pages/friend/friend-remark"}, {"path": "pages/webview/webview"}, {"path": "pages/Jenasi/project/ProjectList"}, {"path": "pages/Jenasi/project/info/plan/plan"}, {"path": "pages/Jenasi/project/info/update/update"}, {"path": "pages/Jenasi/project/info/plan/update/update"}, {"path": "pages/Jenasi/project/info/ProjectDetail"}, {"path": "pages/Jenasi/project/add/add", "style": {"navigationBarTitleText": ""}}, {"path": "pages/Jenasi/project/info/plan/add/add", "style": {"navigationBarTitleText": ""}}, {"path": "pages/Jenasi/project/index/index", "style": {"navigationBarTitleText": ""}}, {"path": "pages/Jenasi/project/tree/tree", "style": {"navigationBarTitleText": ""}}, {"path": "pages/warehouse/mes-factory"}, {"path": "pages/warehouse/my-task", "style": {"navigationBarTitleText": "我的任务"}}, {"path": "pages/warehouse/mytask/myOrderTask", "style": {"navigationBarTitleText": "生产任务", "enablePullDownRefresh": true, "onReachBottomDistance": 50}}, {"path": "pages/warehouse/mytask/bom", "style": {"navigationBarTitleText": "BOM清单", "enablePullDownRefresh": false}}, {"path": "pages/warehouse/mytask/scanCode", "style": {"navigationBarTitleText": "扫码入库第二阶段", "enablePullDownRefresh": false}}, {"path": "pages/warehouse/mytask/scanOutBound", "style": {"navigationBarTitleText": "扫码领料第三阶段", "enablePullDownRefresh": false}}, {"path": "pages/warehouse/mytask/productScanCodeInBound", "style": {"navigationBarTitleText": "扫码入库第三阶段", "enablePullDownRefresh": false}}, {"path": "pages/warehouse/mytask/defect", "style": {"navigationBarTitleText": "提交缺陷", "enablePullDownRefresh": false}}, {"path": "pages/warehouse/my-purchase"}, {"path": "pages/warehouse/scan-center"}, {"path": "pages/warehouse/user-center"}, {"path": "pages/warehouse/inventory/raw-materials"}, {"path": "pages/warehouse/purchase/purchase-management"}, {"path": "pages/warehouse/purchase/purchase-application"}, {"path": "pages/warehouse/purchase/purchase-inbound"}, {"path": "pages/warehouse/purchase/inbound-scan"}, {"path": "pages/warehouse/purchase/purchase-orders"}, {"path": "pages/warehouse/purchase/item-management"}, {"path": "pages/warehouse/purchase/supplier-management"}], "globalStyle": {"navigationBarTitleText": "思信", "navigationStyle": "custom", "navigationBarBackgroundColor": "#f7f7f7", "backgroundColor": "#f7f7f7"}, "tabBar": {"color": "#000000", "selectedColor": "#587ff0", "borderStyle": "black", "backgroundColor": "#ffffff", "list": [{"pagePath": "pages/chat/chat", "iconPath": "static/tarbar/chat.png", "selectedIconPath": "static/tarbar/chat_active.png", "text": "消息"}, {"pagePath": "pages/friend/friend", "iconPath": "static/tarbar/friend.png", "selectedIconPath": "static/tarbar/friend_active.png", "text": "联系人"}, {"pagePath": "pages/workspace/workspace", "iconPath": "static/tarbar/works.png", "selectedIconPath": "static/tarbar/work_active.png", "text": "工作台"}, {"pagePath": "pages/mine/mine", "iconPath": "static/tarbar/mine.png", "selectedIconPath": "static/tarbar/mine_active.png", "text": "我的"}]}, "uniIdRouter": {}}