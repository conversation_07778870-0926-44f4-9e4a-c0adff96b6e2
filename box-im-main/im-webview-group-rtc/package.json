{"name": "web", "version": "0.1.0", "private": true, "scripts": {"serve": "set NODE_OPTIONS=--openssl-legacy-provider & vue-cli-service serve", "build": "set NODE_OPTIONS=--openssl-legacy-provider & vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^1.1.3", "core-js": "^3.6.5", "postcss": "^7.0.32", "postcss-import": "^12.0.1", "postcss-px-to-viewport": "^1.1.1", "sass": "^1.47.0", "sass-loader": "^10.1.1", "vconsole": "^3.15.1", "vue": "^2.6.11", "vue-axios": "^3.5.0", "vant": "^2.13.2", "vant-green": "^1.0.44"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.12", "@vue/cli-plugin-eslint": "~4.5.12", "@vue/cli-service": "~4.5.12", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "vue-template-compiler": "^2.6.11"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {"no-mixed-spaces-and-tabs": 0, "generator-star-spacing": "off", "no-tabs": "off", "no-unused-vars": "off", "no-unused-labels": "off", "no-console": "off", "vue/no-unused-components": "off", "no-irregular-whitespace": "off", "no-debugger": "off", "no-useless-escape": "off"}}, "resolutions": {"postcss": "7.0.32"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}